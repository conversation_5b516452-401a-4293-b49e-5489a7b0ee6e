import { Link } from "@remix-run/react";

export default function Hero() {
  return (
    <div className="bg-gradient-to-br from-purple-50 via-pink-50 to-purple-50 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Empower Your Journey with{" "}
          <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            AI-Powered Tools
          </span>
        </h1>
        <p className="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
          Welcome to HubForWomen - your ultimate destination for career growth, 
          personal development, and success. Leverage cutting-edge AI to excel in life.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="/resume-analyzer"
            className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg"
          >
            Try AI Resume Analyzer
          </Link>
          <Link
            to="/tools"
            className="border-2 border-purple-600 text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-purple-600 hover:text-white transition-all"
          >
            Explore All Tools
          </Link>
        </div>
      </div>
    </div>
  );
}