interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href?: string;
}

export default function FeatureCard({ icon, title, description, href }: FeatureCardProps) {
  const cardContent = (
    <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 border border-gray-100">
      <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );

  if (href) {
    return (
      <a href={href} className="block transform hover:scale-105 transition-transform">
        {cardContent}
      </a>
    );
  }

  return cardContent;
}