import { Link } from "@remix-run/react";

export default function Header() {
  return (
    <header className="bg-gradient-to-r from-purple-600 via-pink-500 to-purple-700 shadow-lg">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex-shrink-0">
            <Link to="/" className="text-2xl font-bold text-white">
              Hub<span className="text-pink-200">For</span>Women
            </Link>
          </div>
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link
                to="/"
                className="text-white hover:text-pink-200 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Home
              </Link>
              <Link
                to="/tools"
                className="text-white hover:text-pink-200 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Tools
              </Link>
              <Link
                to="/resume-analyzer"
                className="bg-white text-purple-600 hover:bg-pink-50 px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                AI Resume Analyzer
              </Link>
            </div>
          </div>
          <div className="md:hidden">
            <button className="text-white hover:text-pink-200">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </nav>
    </header>
  );
}