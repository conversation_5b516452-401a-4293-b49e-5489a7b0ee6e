{"name": "ai-resume-analyzer", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@remix-run/node": "*", "@remix-run/react": "*", "@remix-run/serve": "*", "@rollup/rollup-win32-x64-msvc": "^4.48.0", "isbot": "^4.1.0", "lucide-react": "^0.541.0", "mammoth": "^1.10.0", "pdf-poppler": "^0.2.1", "pdf2json": "^3.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@remix-run/dev": "*", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}