import type { MetaFunction } from "@remix-run/node";
import { useState, useRef } from "react";
import { useNavigate } from "@remix-run/react";
import { AlertTriangle } from 'lucide-react';
import Header from "~/components/Header";
import Footer from "~/components/Footer";


interface ErrorResponse {
  error: string;
  details?: string;
  stack?: string;
}

export const meta: MetaFunction = () => {
  return [
    { title: "AI Resume Analyzer - HubForWomen" },
    { name: "description", content: "Get AI-powered insights to optimize your resume for better job opportunities." },
  ];
};

export default function ResumeAnalyzer() {
  const [file, setFile] = useState<File | null>(null);
  const [coverLetter, setCoverLetter] = useState<string>("");
  const [jobDescription, setJobDescription] = useState<string>("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<ErrorResponse | null>(null);
  const [showCoverLetter<PERSON>ield, setShowCoverLetterField] = useState(false);
  const [showJobDescriptionField, setShowJobDescriptionField] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const handleFileSelect = (selectedFile: File) => {
    if (selectedFile.size > 5 * 1024 * 1024) {
      setError({
        error: "File too large",
        details: `File size is ${(selectedFile.size / (1024 * 1024)).toFixed(1)}MB. Please upload a file smaller than 5MB.`
      });
      return;
    }
    
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!allowedTypes.includes(selectedFile.type)) {
      setError({
        error: "Unsupported file format",
        details: "Please upload a PDF, DOC, or DOCX file. Other formats are not currently supported."
      });
      return;
    }
    
    setFile(selectedFile);
    setError(null);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const analyzeResume = async () => {
    if (!file) {
      setError({ error: "Please upload a resume file before starting the analysis." });
      return;
    }
    
    setIsAnalyzing(true);
    setError(null);
    
    try {
      console.log('Starting analysis for file:', file.name, 'Type:', file.type, 'Size:', file.size);
      
      const formData = new FormData();
      formData.append("file", file);
      if (coverLetter.trim()) {
        formData.append("coverLetter", coverLetter.trim());
      }
      if (jobDescription.trim()) {
        formData.append("jobDescription", jobDescription.trim());
      }

      const response = await fetch("/api/analyze-resume", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Server error response:', errorData);
        setError({
          error: errorData.error || "Failed to analyze resume",
          details: errorData.details,
          stack: errorData.stack
        });
        return;
      }

      const result = await response.json();
      console.log('Analysis successful:', result);
      
      // Navigate to results page with analysis data
      navigate('/cv-analysis-results', {
        state: { 
          analysisResult: result,
          fileName: file.name,
          hasJobDescription: !!jobDescription.trim(),
          hasCoverLetter: !!coverLetter.trim()
        }
      });
    } catch (err) {
      console.error("Frontend analysis error:", err);
      setError({
        error: err instanceof Error ? err.message : "Failed to analyze resume. Please try again.",
        details: err instanceof Error ? err.stack : undefined
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 bg-gradient-to-br from-purple-50 via-pink-50 to-purple-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              AI-Powered{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Resume Analyzer
              </span>
            </h1>
            <p className="text-xl text-gray-700 max-w-2xl mx-auto">
              Upload your resume and get professional recruiter-level analysis with ATS optimization, 
              keyword matching, and targeted feedback to maximize your interview chances.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div
              className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 ${
                file
                  ? 'border-green-400 bg-green-50'
                  : isDragOver
                    ? 'border-purple-500 bg-purple-50 scale-105'
                    : 'border-gray-300 hover:border-purple-400 hover:bg-purple-25'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleFileInputChange}
                className="hidden"
              />
              
              {!file ? (
                <>
                  <div className="mx-auto w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Upload Your Resume</h3>
                  <p className="text-gray-600 mb-4">
                    Drag and drop your resume file here, or click to browse
                  </p>
                  <button 
                    onClick={() => fileInputRef.current?.click()}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg"
                  >
                    Choose File
                  </button>
                  <p className="text-sm text-gray-500 mt-3">
                    Supported formats: PDF, DOC, DOCX (Max 5MB)
                  </p>
                </>
              ) : (
                <div className="text-center">
                  <div className="mx-auto w-16 h-16 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">File Selected</h3>
                  <p className="text-gray-600 mb-4">{file.name}</p>
                  <div className="flex gap-3 justify-center flex-wrap">
                    <button
                      onClick={() => setShowJobDescriptionField(!showJobDescriptionField)}
                      className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg"
                    >
                      {showJobDescriptionField ? 'Hide' : 'Add'} Job Description
                    </button>
                    <button
                      onClick={() => setShowCoverLetterField(!showCoverLetterField)}
                      className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-indigo-600 transition-all transform hover:scale-105 shadow-lg"
                    >
                      {showCoverLetterField ? 'Hide' : 'Add'} Cover Letter
                    </button>
                    <button
                      onClick={() => {
                        setFile(null);
                        setError(null);
                        setCoverLetter("");
                        setJobDescription("");
                        setShowCoverLetterField(false);
                        setShowJobDescriptionField(false);
                        if (fileInputRef.current) fileInputRef.current.value = '';
                      }}
                      className="bg-gray-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-600 transition-colors"
                    >
                      Remove File
                    </button>
                  </div>
                </div>
              )}
            </div>

            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-red-800 font-semibold mb-1">{error.error}</p>
                    {error.details && (
                      <p className="text-red-700 text-sm mb-2">Details: {error.details}</p>
                    )}
                    {error.stack && process.env.NODE_ENV === 'development' && (
                      <details className="mt-2">
                        <summary className="text-red-600 text-sm cursor-pointer">Technical Details (Development Mode)</summary>
                        <pre className="text-xs text-red-600 mt-1 p-2 bg-red-100 rounded overflow-auto">
                          {error.stack}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Job Description Section */}
          {file && showJobDescriptionField && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Target Job Description</h3>
                <p className="text-gray-600">
                  Add the job description you&apos;re applying for to get targeted analysis and ATS optimization recommendations.
                </p>
              </div>
              
              <div className="space-y-4">
                <textarea
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  placeholder="Paste the job description here including requirements, qualifications, and responsibilities..."
                  className="w-full h-48 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                />
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>{jobDescription.length} characters</span>
                  <span>More detailed job descriptions provide better analysis</span>
                </div>
              </div>
            </div>
          )}

          {/* Cover Letter Section */}
          {file && showCoverLetterField && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Cover Letter (Optional)</h3>
                <p className="text-gray-600">
                  Adding a cover letter can enhance the analysis by providing context about your career goals and motivations.
                </p>
              </div>
              
              <div className="space-y-4">
                <textarea
                  value={coverLetter}
                  onChange={(e) => setCoverLetter(e.target.value)}
                  placeholder="Paste your cover letter here or write a brief summary of why you're interested in this role and how your experience aligns..."
                  className="w-full h-40 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                />
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>{coverLetter.length} characters</span>
                  <span>Recommended: 200-500 characters for best analysis</span>
                </div>
              </div>
            </div>
          )}

          {/* Analysis Button */}
          {file && (
            <div className="text-center mb-8">
              <button
                onClick={analyzeResume}
                disabled={isAnalyzing}
                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-12 py-4 rounded-lg text-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                aria-label={isAnalyzing ? "Analyzing resume, please wait" : "Start AI analysis of your resume"}
              >
                {isAnalyzing ? (
                  <div className="flex items-center gap-3">
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Analyzing Your Resume...
                  </div>
                ) : (
                  'Start AI Analysis'
                )}
              </button>
              <p className="text-sm text-gray-500 mt-3">
                {jobDescription.trim() && coverLetter.trim() ? 
                  'Analysis will include your resume, job description, and cover letter for targeted optimization' :
                  jobDescription.trim() ? 
                    'Analysis will target your resume against the specific job description' :
                    coverLetter.trim() ? 
                      'Analysis will include both your resume and cover letter' : 
                      'General resume analysis will be performed'
                }
              </p>
            </div>
          )}


          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white rounded-xl p-6 shadow-lg text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Professional Recruiter Analysis</h3>
              <p className="text-gray-600 text-sm">
                Get insights from the perspective of experienced recruiters and hiring managers
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">ATS Optimization</h3>
              <p className="text-gray-600 text-sm">
                Ensure your resume passes Applicant Tracking Systems with targeted keyword analysis
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Job-Specific Matching</h3>
              <p className="text-gray-600 text-sm">
                Target your resume to specific job descriptions for maximum impact
              </p>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}