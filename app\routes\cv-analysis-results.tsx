import type { MetaFunction } from "@remix-run/node";
import { useLocation, useNavigate } from "@remix-run/react";
import { useEffect, useState } from "react";
import { TrendingUp, Target, Award, AlertTriangle, CheckCircle, Users, Briefcase, DollarSign, Star, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, Download, UserCheck } from 'lucide-react';
import Header from "~/components/Header";
import Footer from "~/components/Footer";

interface AnalysisSection {
  score: number;
  feedback: string;
  improvements: string[];
  keywordAlignment?: number;
  achievementsVsResponsibilities?: string;
  matchedSkills?: string[];
  missingSkills?: string[];
  readabilityIssues?: string[];
  quantifiedAchievements?: number;
  strongActionVerbs?: number;
}


interface KpiSummary {
  atsScore: number;
  jobDescriptionMatch: number | null;
  keywordDensity: number;
  presentKeywordsCount: number;
  missingKeywordsCount: number;
  quantifiableAchievements: number;
  actionVerbCount: number;
  impactScore: number;
  phoneScreenLikelihood: number;
  interviewReadiness: number;
  industryPercentile: number;
}

interface AnalysisResult {
  overallScore: number;
  firstImpressionScore?: number;
  sections: {
    atsCompatibility: AnalysisSection;
    contentQuality: AnalysisSection;
    skillsMatching: AnalysisSection;
    formatting: AnalysisSection;
    experience: AnalysisSection;
  };
  strengths: string[];
  weaknesses: string[];
  keywordAnalysis: {
    presentKeywords: string[];
    missingKeywords: string[];
    keywordDensity: number;
    jobDescriptionMatch?: number;
    keywordIntegration?: string;
  };
  careerAnalysis: {
    currentLevel: string;
    industryFocus: string;
    suggestedRoles: string[];
    careerPath: string;
    salaryEstimate: string;
    marketability?: string;
  };
  impactMetrics: {
    quantifiableAchievements: number;
    actionVerbs: number;
    impactScore: number;
    leadershipExamples?: number;
    problemSolvingExamples?: number;
  };
  detailedFeedback: {
    summary: string;
    professionalSummary: string;
    workExperience: string;
    education: string;
    skills: string;
    additionalSections: string;
    atsOptimization?: string;
  };
  priorityActions: string[];
  industryBenchmark: {
    percentile: number;
    comparison: string;
    competitiveAdvantages?: string[];
    marketGaps?: string[];
  };
  recruiterPerspective?: {
    sixSecondImpression: string;
    phoneScreenLikelihood: number;
    interviewReadiness: number;
    redFlags: string[];
    positiveSignals: string[];
  };
  hasCoverLetter?: boolean;
  hasJobDescription?: boolean;
  kpiSummary?: KpiSummary;
}

export const meta: MetaFunction = () => {
  return [
    { title: "CV Analysis Results - HubForWomen" },
    { name: "description", content: "Comprehensive AI-powered analysis of your CV with recruiter insights and optimization recommendations." },
  ];
};

export default function CVAnalysisResults() {
  const location = useLocation();
  const navigate = useNavigate();
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [fileName, setFileName] = useState<string>("");
  const [hasJobDescription, setHasJobDescription] = useState<boolean>(false);
  const [hasCoverLetter, setHasCoverLetter] = useState<boolean>(false);

  useEffect(() => {
    const state = location.state as {
      analysisResult?: AnalysisResult;
      fileName?: string;
      hasJobDescription?: boolean;
      hasCoverLetter?: boolean;
    };

    if (!state?.analysisResult) {
      // Redirect back to resume analyzer if no data
      navigate('/resume-analyzer');
      return;
    }

    setAnalysisResult(state.analysisResult);
    setFileName(state.fileName || "Resume");
    setHasJobDescription(state.hasJobDescription || false);
    setHasCoverLetter(state.hasCoverLetter || false);
  }, [location.state, navigate]);

  if (!analysisResult) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading analysis results...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 bg-gradient-to-br from-purple-50 via-pink-50 to-purple-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header with Back Button */}
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => navigate('/resume-analyzer')}
              className="flex items-center gap-2 text-purple-600 hover:text-purple-700 font-medium focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 rounded-md px-2 py-1"
              aria-label="Go back to analyze another resume"
            >
              <ArrowLeft className="w-5 h-5" />
              Analyze Another Resume
            </button>
            <div className="text-sm text-gray-500">
              Analysis for: {fileName}
            </div>
          </div>

          <div className="space-y-10">
            {/* Header Summary */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <div className="text-center mb-8">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                  CV Analysis Complete!
                </h1>
                <div className="flex justify-center items-center gap-8 mb-6">
                  <div className="relative w-32 h-32">
                    <div className="w-32 h-32 rounded-full border-8 border-gray-200 relative">
                      <div
                        className="absolute inset-0 rounded-full border-8 border-purple-600 transition-all duration-1000"
                        style={{
                          transform: `rotate(-90deg)`,
                          background: `conic-gradient(#8B5CF6 0deg, #8B5CF6 ${(analysisResult.overallScore / 100) * 360}deg, transparent ${(analysisResult.overallScore / 100) * 360}deg)`,
                          borderRadius: '50%',
                          mask: 'radial-gradient(circle at center, transparent 50%, white 50%)'
                        }}
                      ></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-3xl font-bold text-purple-600">{analysisResult.overallScore}%</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <h2 className="text-2xl font-bold text-gray-900">
                      Overall {hasCoverLetter ? 'Application' : 'CV'} Score
                    </h2>
                    <p className="text-lg text-gray-600 mt-2">{analysisResult.detailedFeedback?.summary}</p>
                    <div className="flex gap-2 mt-4">
                      {hasCoverLetter && (
                        <div className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Cover Letter Included
                        </div>
                      )}
                      {hasJobDescription && (
                        <div className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                          <Target className="w-4 h-4 mr-1" />
                          Job-Targeted Analysis
                        </div>
                      )}
                    </div>
                  </div>

            {/* Interactive Key Metrics */}
            <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <BarChartIcon className="w-6 h-6 text-purple-600 mr-3" />
                Key Metrics Overview
              </h2>
              
              {(() => {
                const kpi = analysisResult.kpiSummary || {
                  atsScore: analysisResult.sections?.atsCompatibility?.score ?? 0,
                  jobDescriptionMatch: analysisResult.keywordAnalysis?.jobDescriptionMatch ?? null,
                  keywordDensity: analysisResult.keywordAnalysis?.keywordDensity ?? 0,
                  presentKeywordsCount: analysisResult.keywordAnalysis?.presentKeywords?.length ?? 0,
                  missingKeywordsCount: analysisResult.keywordAnalysis?.missingKeywords?.length ?? 0,
                  quantifiableAchievements: analysisResult.impactMetrics?.quantifiableAchievements ?? analysisResult.sections?.experience?.quantifiedAchievements ?? 0,
                  actionVerbCount: analysisResult.impactMetrics?.actionVerbs ?? analysisResult.sections?.experience?.strongActionVerbs ?? 0,
                  impactScore: analysisResult.impactMetrics?.impactScore ?? analysisResult.overallScore ?? 0,
                  phoneScreenLikelihood: analysisResult.recruiterPerspective?.phoneScreenLikelihood ?? 0,
                  interviewReadiness: analysisResult.recruiterPerspective?.interviewReadiness ?? 0,
                  industryPercentile: analysisResult.industryBenchmark?.percentile ?? 0,
                } as KpiSummary;

                // Prepare data for the pie chart
                const chartData = [
                  { name: 'ATS Score', value: kpi.atsScore, color: '#8B5CF6', lightColor: '#F3E8FF' },
                  { name: 'Content Quality', value: analysisResult.sections?.contentQuality?.score ?? 0, color: '#06B6D4', lightColor: '#E0F7FA' },
                  { name: 'Skills Match', value: analysisResult.sections?.skillsMatching?.score ?? 0, color: '#10B981', lightColor: '#ECFDF5' },
                  { name: 'Formatting', value: analysisResult.sections?.formatting?.score ?? 0, color: '#F59E0B', lightColor: '#FEF3C7' },
                  { name: 'Experience', value: analysisResult.sections?.experience?.score ?? 0, color: '#EF4444', lightColor: '#FEE2E2' }
                ];

                const totalValue = chartData.reduce((sum, item) => sum + item.value, 0);
                let cumulativeAngle = 0;

                return (
                  <div className="grid lg:grid-cols-2 gap-8 items-center">
                    {/* Interactive Pie Chart */}
                    <div className="flex flex-col items-center">
                      <div className="relative w-64 h-64 mb-4">
                        <svg width="256" height="256" viewBox="0 0 256 256" className="transform -rotate-90">
                          {chartData.map((segment, index) => {
                            const angle = (segment.value / totalValue) * 360;
                            const startAngle = cumulativeAngle;
                            const endAngle = startAngle + angle;
                            
                            const startAngleRad = (startAngle * Math.PI) / 180;
                            const endAngleRad = (endAngle * Math.PI) / 180;
                            
                            const largeArcFlag = angle > 180 ? 1 : 0;
                            
                            const startX = 128 + 100 * Math.cos(startAngleRad);
                            const startY = 128 + 100 * Math.sin(startAngleRad);
                            const endX = 128 + 100 * Math.cos(endAngleRad);
                            const endY = 128 + 100 * Math.sin(endAngleRad);
                            
                            const pathData = `M 128 128 L ${startX} ${startY} A 100 100 0 ${largeArcFlag} 1 ${endX} ${endY} Z`;
                            
                            cumulativeAngle = endAngle;
                            
                            return (
                              <g key={index}>
                                <path
                                  d={pathData}
                                  fill={segment.color}
                                  className="cursor-pointer transition-all duration-300 hover:opacity-80 hover:scale-105"
                                  style={{ transformOrigin: '128px 128px' }}
                                />
                                {/* Score text on each segment */}
                                {angle > 30 && (
                                  <text
                                    x={128 + 60 * Math.cos((startAngleRad + endAngleRad) / 2)}
                                    y={128 + 60 * Math.sin((startAngleRad + endAngleRad) / 2)}
                                    fill="white"
                                    fontSize="14"
                                    fontWeight="bold"
                                    textAnchor="middle"
                                    dominantBaseline="middle"
                                    className="transform rotate-90"
                                    style={{ transformOrigin: `${128 + 60 * Math.cos((startAngleRad + endAngleRad) / 2)}px ${128 + 60 * Math.sin((startAngleRad + endAngleRad) / 2)}px` }}
                                  >
                                    {segment.value}%
                                  </text>
                                )}
                              </g>
                            );
                          })}
                          {/* Center circle with overall score */}
                          <circle
                            cx="128"
                            cy="128"
                            r="45"
                            fill="white"
                            stroke="#E5E7EB"
                            strokeWidth="2"
                          />
                          <text
                            x="128"
                            y="120"
                            fill="#374151"
                            fontSize="20"
                            fontWeight="bold"
                            textAnchor="middle"
                            className="transform rotate-90"
                            style={{ transformOrigin: '128px 120px' }}
                          >
                            {analysisResult.overallScore}%
                          </text>
                          <text
                            x="128"
                            y="140"
                            fill="#6B7280"
                            fontSize="12"
                            textAnchor="middle"
                            className="transform rotate-90"
                            style={{ transformOrigin: '128px 140px' }}
                          >
                            Overall
                          </text>
                        </svg>
                      </div>
                      
                      {/* Legend */}
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        {chartData.map((item, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: item.color }}
                            ></div>
                            <span className="text-gray-700">{item.name}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Key Metrics Cards */}
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      {[
                        { label: 'Phone Screen Likelihood', value: `${kpi.phoneScreenLikelihood}%`, color: 'bg-indigo-50 text-indigo-700', icon: '📞' },
                        { label: 'Interview Readiness', value: `${kpi.interviewReadiness}%`, color: 'bg-green-50 text-green-700', icon: '💼' },
                        { label: 'Industry Percentile', value: `${kpi.industryPercentile}th`, color: 'bg-blue-50 text-blue-700', icon: '📊' },
                        { label: 'Keywords Found', value: `${kpi.presentKeywordsCount}`, color: 'bg-emerald-50 text-emerald-700', icon: '🔍' },
                        { label: 'Keywords Missing', value: `${kpi.missingKeywordsCount}`, color: 'bg-amber-50 text-amber-700', icon: '⚠️' },
                        { label: 'Impact Score', value: `${kpi.impactScore}%`, color: 'bg-purple-50 text-purple-700', icon: '⚡' }
                      ].concat(kpi.jobDescriptionMatch !== null ? [
                        { label: 'Job Match', value: `${kpi.jobDescriptionMatch}%`, color: 'bg-cyan-50 text-cyan-700', icon: '🎯' }
                      ] : []).map((metric, index) => (
                        <div key={index} className={`rounded-xl p-4 text-center border border-opacity-20 hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer ${metric.color}`}>
                          <div className="text-2xl mb-1">{metric.icon}</div>
                          <div className="text-xl font-bold mb-1">{metric.value}</div>
                          <div className="text-xs opacity-80 font-medium leading-tight">{metric.label}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })()}
            </div>

                </div>

                {/* Industry Benchmark */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                  <div className="flex items-center justify-center gap-4">
                    <Target className="w-8 h-8 text-blue-600" />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Industry Benchmark</h3>
                      <p className="text-sm text-gray-600">
                        {analysisResult.industryBenchmark?.percentile}th percentile - {analysisResult.industryBenchmark?.comparison}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recruiter Perspective - First for maximum impact */}
            {analysisResult.recruiterPerspective && (
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <UserCheck className="w-6 h-6 text-indigo-600 mr-3" />
                  Recruiter&apos;s First Impression
                </h2>

                <div className="space-y-6 mb-8">
                  <div className="bg-indigo-50 rounded-xl p-6">
                    <div className="flex items-center gap-6">
                      <div className="flex-shrink-0 text-center">
                        <div className="text-4xl font-bold text-indigo-600 mb-1">
                          {analysisResult.recruiterPerspective.phoneScreenLikelihood}%
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Phone Screen Likelihood</h3>
                        <p className="text-base text-gray-700">Chance of getting initial call based on resume quality and relevance</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 rounded-xl p-6">
                    <div className="flex items-center gap-6">
                      <div className="flex-shrink-0 text-center">
                        <div className="text-4xl font-bold text-green-600 mb-1">
                          {analysisResult.recruiterPerspective.interviewReadiness}%
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Interview Readiness</h3>
                        <p className="text-base text-gray-700">Overall preparedness and presentation quality for interview process</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-50 rounded-xl p-6">
                    <div className="flex items-start gap-6">
                      <div className="flex-shrink-0 text-center">
                        <div className="text-2xl font-bold text-purple-600 mb-1">
                          6-Second Scan
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">First Impression</h3>
                        <p className="text-base text-gray-700 leading-relaxed">{analysisResult.recruiterPerspective.sixSecondImpression}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-semibold text-green-600 mb-4">What Recruiters Love</h3>
                    <div className="space-y-3">
                      {analysisResult.recruiterPerspective.positiveSignals.map((signal: string, index: number) => (
                        <div key={index} className="flex items-start gap-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{signal}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-red-600 mb-4">Potential Concerns</h3>
                    <div className="space-y-3">
                      {analysisResult.recruiterPerspective.redFlags.map((flag: string, index: number) => (
                        <div key={index} className="flex items-start gap-3">
                          <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{flag}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Priority Actions - High visibility */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Target className="w-6 h-6 text-red-600 mr-3" />
                Priority Actions (Ranked by Impact)
              </h2>
              <div className="space-y-4">
                {(analysisResult.priorityActions || []).map((action: string, index: number) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border-l-4 border-red-500">
                    <span className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                      {index + 1}
                    </span>
                    <div>
                      <p className="text-gray-800 font-medium">{action}</p>
                      <p className="text-sm text-gray-600 mt-1">High impact improvement</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Detailed Score Breakdown */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <BarChartIcon className="w-6 h-6 text-purple-600 mr-3" />
                Detailed Score Analysis
              </h2>

              <div className="space-y-6 mb-8">
                {Object.entries(analysisResult.sections || {}).map(([key, section]) => {
                  const getScoreColor = (score: number) => {
                    if (score >= 80) return 'text-green-600';
                    if (score >= 60) return 'text-yellow-600';
                    return 'text-red-600';
                  };

                  const sectionNames = {
                    atsCompatibility: 'ATS Compatibility',
                    contentQuality: 'Content Quality',
                    skillsMatching: 'Skills Matching',
                    formatting: 'Formatting & Layout',
                    experience: 'Work Experience'
                  };

                  return (
                    <div key={key} className="bg-gray-50 rounded-xl p-6">
                      <div className="flex items-start gap-6">
                        <div className="flex-shrink-0 text-center">
                          <div className={`text-4xl font-bold mb-1 ${getScoreColor(section.score)}`}>
                            {section.score}%
                          </div>
                          <div className="w-20 bg-gray-200 rounded-full h-3">
                            <div
                              className={`h-3 rounded-full transition-all duration-500 ${
                                section.score >= 80 ? 'bg-green-500' :
                                section.score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${section.score}%` }}
                            ></div>
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-semibold text-gray-900 mb-3">
                            {sectionNames[key as keyof typeof sectionNames] || key}
                          </h3>
                          <p className="text-base text-gray-700 leading-relaxed">{section.feedback}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Visual Performance Chart */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Performance Overview</h3>
                {Object.entries(analysisResult.sections || {}).map(([key, section]) => {
                  const sectionNames = {
                    atsCompatibility: 'ATS Compatibility',
                    contentQuality: 'Content Quality',
                    skillsMatching: 'Skills Matching',
                    formatting: 'Formatting',
                    experience: 'Experience'
                  };

                  return (
                    <div key={key} className="flex items-center gap-4">
                      <div className="w-32 text-sm font-medium text-gray-700">
                        {sectionNames[key as keyof typeof sectionNames] || key}
                      </div>
                      <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                        <div
                          className={`h-4 rounded-full transition-all duration-1000 ${
                            section.score >= 80 ? 'bg-gradient-to-r from-green-400 to-green-600' :
                            section.score >= 60 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' :
                            'bg-gradient-to-r from-red-400 to-red-600'
                          }`}
                          style={{ width: `${section.score}%` }}
                        ></div>
                        <span className="absolute right-2 top-0 h-4 flex items-center text-xs font-semibold text-gray-600">
                          {section.score}%
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Strengths and Weaknesses */}
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <Award className="w-6 h-6 text-green-600 mr-3" />
                  Key Strengths
                </h2>
                <div className="space-y-4">
                  {(analysisResult.strengths || []).map((strength: string, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{strength}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <TrendingUp className="w-6 h-6 text-orange-600 mr-3" />
                  Improvement Opportunities
                </h2>
                <div className="space-y-4">
                  {(analysisResult.weaknesses || []).map((weakness: string, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                      <TrendingUp className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{weakness}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Career Analysis */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Briefcase className="w-6 h-6 text-blue-600 mr-3" />
                Career Analysis & Market Positioning
              </h2>

              <div className="grid md:grid-cols-3 gap-8 mb-8">
                <div className="bg-blue-50 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <Users className="w-6 h-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Current Level</h3>
                  </div>
                  <p className="text-gray-700 text-lg capitalize">{analysisResult.careerAnalysis?.currentLevel}</p>
                  <p className="text-sm text-gray-600 mt-2">Industry: {analysisResult.careerAnalysis?.industryFocus}</p>
                </div>

                <div className="bg-green-50 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <DollarSign className="w-6 h-6 text-green-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Market Value</h3>
                  </div>
                  <p className="text-gray-700 text-lg font-semibold">{analysisResult.careerAnalysis?.salaryEstimate}</p>
                  <p className="text-sm text-gray-600 mt-2">Based on current profile</p>
                </div>

                <div className="bg-purple-50 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <Star className="w-6 h-6 text-purple-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Impact Score</h3>
                  </div>
                  <p className="text-gray-700 text-lg font-semibold">{analysisResult.impactMetrics?.impactScore}%</p>
                  <p className="text-sm text-gray-600 mt-2">{analysisResult.impactMetrics?.quantifiableAchievements} quantified achievements</p>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Career Development Path</h3>
                  <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{analysisResult.careerAnalysis?.careerPath}</p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Recommended Target Roles</h3>
                  <div className="flex flex-wrap gap-2">
                    {(analysisResult.careerAnalysis?.suggestedRoles || []).map((role: string, index: number) => (
                      <span key={index} className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                        {role}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Keyword Analysis */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Keyword & ATS Optimization</h2>

              <div className="grid md:grid-cols-2 gap-8 mb-6">
                <div>
                  <h3 className="text-lg font-semibold text-green-600 mb-4">Keywords Found</h3>
                  <div className="flex flex-wrap gap-2">
                    {(analysisResult.keywordAnalysis?.presentKeywords || []).map((keyword: string, index: number) => (
                      <span key={index} className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-orange-600 mb-4">Add These Keywords</h3>
                  <div className="flex flex-wrap gap-2">
                    {(analysisResult.keywordAnalysis?.missingKeywords || []).map((keyword: string, index: number) => (
                      <span key={index} className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Keyword Density</span>
                    <span className="text-sm text-gray-500">{analysisResult.keywordAnalysis?.keywordDensity}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-purple-600 to-pink-600 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${analysisResult.keywordAnalysis?.keywordDensity}%` }}
                    ></div>
                  </div>
                </div>

                {analysisResult.keywordAnalysis?.jobDescriptionMatch && (
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Job Description Match</span>
                      <span className="text-sm text-gray-500">{analysisResult.keywordAnalysis.jobDescriptionMatch}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-blue-600 to-indigo-600 h-3 rounded-full transition-all duration-500"
                        style={{ width: `${analysisResult.keywordAnalysis.jobDescriptionMatch}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {analysisResult.keywordAnalysis?.keywordIntegration && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Keyword Integration Assessment</h4>
                    <p className="text-sm text-gray-700">{analysisResult.keywordAnalysis.keywordIntegration}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Detailed Section Feedback */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Section-by-Section Analysis</h2>
              <div className="grid gap-6">
                {Object.entries(analysisResult.detailedFeedback || {}).map(([section, feedback]) => (
                  <div key={section} className="border-l-4 border-purple-500 pl-6 bg-gray-50 p-4 rounded-r-lg">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 capitalize">
                      {section.replace(/([A-Z])/g, ' $1').trim()}
                    </h3>
                    <p className="text-gray-700">{feedback}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
              <div className="text-center mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">What's Next?</h2>
                <p className="text-gray-600">Take action on your analysis results</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => navigate('/resume-analyzer')}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                >
                  Analyze Another CV
                </button>
                <button
                  className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:from-blue-600 hover:to-indigo-600 transition-all transform hover:scale-105 shadow-lg flex items-center gap-2 justify-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  disabled
                  title="Coming soon"
                >
                  <Download className="w-5 h-5" />
                  Download Report
                </button>
                <button
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:from-green-600 hover:to-emerald-600 transition-all transform hover:scale-105 shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  disabled
                  title="Coming soon"
                >
                  Get Career Coaching
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}